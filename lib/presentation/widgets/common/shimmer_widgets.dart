import 'package:flutter/material.dart';
import 'loading_widget.dart';

// Modern Dashboard shimmer with staggered animations and better visual hierarchy
class DashboardShimmer extends StatelessWidget {
  const DashboardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        title: const Shimmer<PERSON><PERSON>(width: 180, height: 24),
        centerTitle: false,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome text shimmer with better spacing
              const _AnimatedShimmerLine(
                width: 220,
                height: 36,
                delay: Duration(milliseconds: 0),
              ),
              const SizedBox(height: 8),
              const _AnimatedShimmerLine(
                width: 160,
                height: 18,
                delay: Duration(milliseconds: 100),
              ),
              const SizedBox(height: 32),

              // Dashboard cards shimmer with staggered animation
              Row(
                children: [
                  Expanded(
                    child: _ModernDashboardCardShimmer(
                      delay: Duration(milliseconds: 200),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _ModernDashboardCardShimmer(
                      delay: Duration(milliseconds: 300),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // Recent activity section header
              const _AnimatedShimmerLine(
                width: 180,
                height: 24,
                delay: Duration(milliseconds: 400),
              ),
              const SizedBox(height: 16),

              // Recent activity card with modern design
              _ModernActivityCardShimmer(
                delay: Duration(milliseconds: 500),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: _ModernBottomNavShimmer(),
    );
  }
}

// Animated shimmer line with delay for staggered animations
class _AnimatedShimmerLine extends StatefulWidget {
  final double width;
  final double height;
  final Duration delay;

  const _AnimatedShimmerLine({
    required this.width,
    required this.height,
    required this.delay,
  });

  @override
  State<_AnimatedShimmerLine> createState() => _AnimatedShimmerLineState();
}

class _AnimatedShimmerLineState extends State<_AnimatedShimmerLine>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    Future.delayed(widget.delay, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Opacity(
          opacity: _animation.value,
          child: Transform.scale(
            scale: 0.8 + (0.2 * _animation.value),
            child: ShimmerLine(
              width: widget.width,
              height: widget.height,
            ),
          ),
        );
      },
    );
  }
}

// Modern dashboard card shimmer with gradient and shadow effects
class _ModernDashboardCardShimmer extends StatelessWidget {
  final Duration delay;

  const _ModernDashboardCardShimmer({required this.delay});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 800),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.9 + (0.1 * value),
          child: Opacity(
            opacity: value,
            child: Container(
              height: 140,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: isDark
                      ? [
                          const Color(0xFF1F2937),
                          const Color(0xFF374151),
                        ]
                      : [
                          const Color(0xFFFFFFFF),
                          const Color(0xFFF9FAFB),
                        ],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: isDark
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Icon shimmer with modern styling
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        gradient: RadialGradient(
                          colors: isDark
                              ? [
                                  const Color(0xFF4F46E5),
                                  const Color(0xFF3B82F6),
                                ]
                              : [
                                  const Color(0xFF6366F1),
                                  const Color(0xFF8B5CF6),
                                ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF6366F1).withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Title shimmer
                    const ShimmerLine(height: 20),
                    const SizedBox(height: 8),
                    // Subtitle shimmer
                    const ShimmerLine(width: 120, height: 16),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// Modern activity card shimmer with sophisticated design
class _ModernActivityCardShimmer extends StatelessWidget {
  final Duration delay;

  const _ModernActivityCardShimmer({required this.delay});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              height: 320,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: isDark
                      ? [
                          const Color(0xFF1F2937),
                          const Color(0xFF374151),
                        ]
                      : [
                          const Color(0xFFFFFFFF),
                          const Color(0xFFF9FAFB),
                        ],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: isDark
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with title and action button
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const ShimmerLine(width: 160, height: 24),
                        Container(
                          width: 80,
                          height: 32,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: isDark
                                  ? [
                                      const Color(0xFF4F46E5),
                                      const Color(0xFF3B82F6),
                                    ]
                                  : [
                                      const Color(0xFF6366F1),
                                      const Color(0xFF8B5CF6),
                                    ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Activity items with staggered animation
                    Expanded(
                      child: ListView.separated(
                        itemCount: 4,
                        separatorBuilder: (context, index) => const SizedBox(height: 16),
                        itemBuilder: (context, index) => _ModernActivityItemShimmer(
                          delay: Duration(milliseconds: 600 + (index * 100)),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// Modern bottom navigation shimmer with glass effect
class _ModernBottomNavShimmer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 90,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: isDark
              ? [
                  const Color(0xFF1F2937).withValues(alpha: 0.9),
                  const Color(0xFF111827),
                ]
              : [
                  const Color(0xFFFFFFFF).withValues(alpha: 0.9),
                  const Color(0xFFF9FAFB),
                ],
        ),
        border: Border(
          top: BorderSide(
            color: isDark
                ? const Color(0xFF374151)
                : const Color(0xFFE5E7EB),
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: List.generate(
            5, // 5 navigation items
            (index) => TweenAnimationBuilder<double>(
              duration: Duration(milliseconds: 600 + (index * 100)),
              tween: Tween(begin: 0.0, end: 1.0),
              builder: (context, value, child) {
                return Transform.scale(
                  scale: 0.8 + (0.2 * value),
                  child: Opacity(
                    opacity: value,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 28,
                          height: 28,
                          decoration: BoxDecoration(
                            gradient: RadialGradient(
                              colors: isDark
                                  ? [
                                      const Color(0xFF374151),
                                      const Color(0xFF1F2937),
                                    ]
                                  : [
                                      const Color(0xFFE5E7EB),
                                      const Color(0xFFF3F4F6),
                                    ],
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        const SizedBox(height: 6),
                        const ShimmerLine(width: 40, height: 12),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

// Resume card shimmer - improved to match actual resume card layout
class ResumeCardShimmer extends StatelessWidget {
  const ResumeCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row with title and menu
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Resume title
                      const ShimmerLine(
                        width: 180,
                        height: 22,
                      ),
                      const SizedBox(height: 6),
                      // Resume subtitle
                      const ShimmerLine(
                        width: 140,
                        height: 16,
                      ),
                    ],
                  ),
                ),
                // Menu button
                const ShimmerBox(
                  width: 24,
                  height: 24,
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Status and date row
            Row(
              children: [
                // Status icon
                const ShimmerCircle(size: 16),
                const SizedBox(width: 8),
                // Status text
                const ShimmerLine(
                  width: 80,
                  height: 14,
                ),
                const Spacer(),
                // Date text
                const ShimmerLine(
                  width: 100,
                  height: 14,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Template card shimmer - improved design
class TemplateCardShimmer extends StatelessWidget {
  const TemplateCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Template preview area
          Expanded(
            flex: 3,
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: const ShimmerBox(
                width: double.infinity,
                height: double.infinity,
                borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
              ),
            ),
          ),

          // Template info area
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Template name
                  const ShimmerLine(
                    width: 120,
                    height: 16,
                  ),
                  const SizedBox(height: 8),
                  // Template category
                  const ShimmerLine(
                    width: 80,
                    height: 12,
                  ),
                  const SizedBox(height: 8),
                  // Bottom row with price and favorite
                  Row(
                    children: [
                      const ShimmerBox(
                        width: 60,
                        height: 20,
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                      ),
                      const Spacer(),
                      const ShimmerCircle(size: 20),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Activity item shimmer - improved layout
class ActivityItemShimmer extends StatelessWidget {
  const ActivityItemShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const ShimmerCircle(size: 40),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Activity title
              const ShimmerLine(
                width: 180,
                height: 16,
              ),
              const SizedBox(height: 6),
              // Activity description
              const ShimmerLine(
                width: 140,
                height: 12,
              ),
            ],
          ),
        ),
        // Time stamp
        const ShimmerLine(
          width: 60,
          height: 12,
        ),
      ],
    );
  }
}

// Form section shimmer
class FormSectionShimmer extends StatelessWidget {
  const FormSectionShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const ShimmerLine(width: 150, height: 20),
          const SizedBox(height: 16),
          const ShimmerBox(width: double.infinity, height: 56),
          const SizedBox(height: 16),
          const ShimmerBox(width: double.infinity, height: 56),
          const SizedBox(height: 16),
          const ShimmerBox(width: double.infinity, height: 100),
        ],
      ),
    );
  }
}

// Resume list shimmer
class ResumeListShimmer extends StatelessWidget {
  const ResumeListShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 6,
      itemBuilder: (context, index) => const ResumeCardShimmer(),
    );
  }
}

// Template grid shimmer
class TemplateGridShimmer extends StatelessWidget {
  const TemplateGridShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: 8,
      itemBuilder: (context, index) => const TemplateCardShimmer(),
    );
  }
}

// Activity list shimmer
class ActivityListShimmer extends StatelessWidget {
  const ActivityListShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: 10,
      separatorBuilder: (context, index) => const SizedBox(height: 16),
      itemBuilder: (context, index) => const ActivityItemShimmer(),
    );
  }
}

// Modern Resume Builder shimmer with realistic form layout
class ResumeBuilderShimmer extends StatelessWidget {
  const ResumeBuilderShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return DefaultTabController(
      length: 8,
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          title: const ShimmerLine(width: 180, height: 24),
          centerTitle: false,
          actions: [
            // Preview button shimmer
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: isDark
                        ? [const Color(0xFF4F46E5), const Color(0xFF3B82F6)]
                        : [const Color(0xFF6366F1), const Color(0xFF8B5CF6)],
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
            // Export button shimmer
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: isDark
                        ? [const Color(0xFF059669), const Color(0xFF10B981)]
                        : [const Color(0xFF10B981), const Color(0xFF34D399)],
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
            // Menu button shimmer
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ShimmerCircle(size: 40),
            ),
          ],
          bottom: TabBar(
            isScrollable: true,
            indicatorColor: Theme.of(context).colorScheme.primary,
            tabs: [
              _ModernTabShimmer(icon: Icons.person, text: 'Personal', delay: 0),
              _ModernTabShimmer(icon: Icons.description, text: 'Summary', delay: 100),
              _ModernTabShimmer(icon: Icons.work, text: 'Experience', delay: 200),
              _ModernTabShimmer(icon: Icons.school, text: 'Education', delay: 300),
              _ModernTabShimmer(icon: Icons.star, text: 'Skills', delay: 400),
              _ModernTabShimmer(icon: Icons.code, text: 'Projects', delay: 500),
              _ModernTabShimmer(icon: Icons.verified, text: 'Certificates', delay: 600),
              _ModernTabShimmer(icon: Icons.language, text: 'Languages', delay: 700),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            ModernFormSectionShimmer(sectionType: FormSectionType.personal),
            ModernFormSectionShimmer(sectionType: FormSectionType.summary),
            ModernFormSectionShimmer(sectionType: FormSectionType.experience),
            ModernFormSectionShimmer(sectionType: FormSectionType.education),
            ModernFormSectionShimmer(sectionType: FormSectionType.skills),
            ModernFormSectionShimmer(sectionType: FormSectionType.projects),
            ModernFormSectionShimmer(sectionType: FormSectionType.certificates),
            ModernFormSectionShimmer(sectionType: FormSectionType.languages),
          ],
        ),
        floatingActionButton: TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 1200),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, value, child) {
            return Transform.scale(
              scale: 0.8 + (0.2 * value),
              child: Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: isDark
                        ? [const Color(0xFF4F46E5), const Color(0xFF3B82F6)]
                        : [const Color(0xFF6366F1), const Color(0xFF8B5CF6)],
                  ),
                  borderRadius: BorderRadius.circular(28),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF6366F1).withValues(alpha: 0.4),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.save,
                  color: Colors.white,
                  size: 28,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

// Modern tab shimmer with icon and text
class _ModernTabShimmer extends StatelessWidget {
  final IconData icon;
  final String text;
  final int delay;

  const _ModernTabShimmer({
    required this.icon,
    required this.text,
    required this.delay,
  });

  @override
  Widget build(BuildContext context) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 800 + delay),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: Tab(
            icon: Icon(icon, size: 20),
            child: ShimmerLine(width: text.length * 8.0, height: 14),
          ),
        );
      },
    );
  }
}

// Form section types for different shimmer layouts
enum FormSectionType {
  personal,
  summary,
  experience,
  education,
  skills,
  projects,
  certificates,
  languages,
}

// Modern form section shimmer that matches actual form layouts
class ModernFormSectionShimmer extends StatelessWidget {
  final FormSectionType sectionType;

  const ModernFormSectionShimmer({
    super.key,
    required this.sectionType,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 600),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.translate(
                offset: Offset(0, 20 * (1 - value)),
                child: Opacity(
                  opacity: value,
                  child: const ShimmerLine(width: 200, height: 28),
                ),
              );
            },
          ),
          const SizedBox(height: 24),

          // Form cards based on section type
          ..._buildFormCards(context, isDark),
        ],
      ),
    );
  }

  List<Widget> _buildFormCards(BuildContext context, bool isDark) {
    switch (sectionType) {
      case FormSectionType.personal:
        return [
          _buildModernFormCard(
            context,
            isDark,
            'Basic Information',
            [
              _buildFormFieldRow([
                _buildFormField('First Name', Icons.person),
                _buildFormField('Last Name', Icons.person_outline),
              ]),
            ],
            delay: 200,
          ),
          const SizedBox(height: 16),
          _buildModernFormCard(
            context,
            isDark,
            'Contact Information',
            [
              _buildFormField('Email Address', Icons.email),
              const SizedBox(height: 16),
              _buildFormField('Phone Number', Icons.phone),
              const SizedBox(height: 16),
              _buildFormField('Address', Icons.location_on),
            ],
            delay: 400,
          ),
        ];

      case FormSectionType.summary:
        return [
          _buildModernFormCard(
            context,
            isDark,
            'Professional Summary',
            [
              _buildFormField('Summary', Icons.description, isTextArea: true),
            ],
            delay: 200,
          ),
        ];

      case FormSectionType.experience:
        return [
          _buildModernFormCard(
            context,
            isDark,
            'Work Experience',
            [
              _buildFormField('Job Title', Icons.work),
              const SizedBox(height: 16),
              _buildFormField('Company', Icons.business),
              const SizedBox(height: 16),
              _buildFormFieldRow([
                _buildFormField('Start Date', Icons.calendar_today),
                _buildFormField('End Date', Icons.calendar_today),
              ]),
              const SizedBox(height: 16),
              _buildFormField('Description', Icons.description, isTextArea: true),
            ],
            delay: 200,
          ),
        ];

      default:
        return [
          _buildModernFormCard(
            context,
            isDark,
            'Section Content',
            [
              _buildFormField('Field 1', Icons.edit),
              const SizedBox(height: 16),
              _buildFormField('Field 2', Icons.edit),
              const SizedBox(height: 16),
              _buildFormField('Field 3', Icons.edit, isTextArea: true),
            ],
            delay: 200,
          ),
        ];
    }
  }

  Widget _buildModernFormCard(
    BuildContext context,
    bool isDark,
    String title,
    List<Widget> children,
    {required int delay}
  ) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 800 + delay),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: isDark
                      ? [
                          const Color(0xFF1F2937),
                          const Color(0xFF374151),
                        ]
                      : [
                          const Color(0xFFFFFFFF),
                          const Color(0xFFF9FAFB),
                        ],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: isDark
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ShimmerLine(width: title.length * 12.0, height: 20),
                    const SizedBox(height: 20),
                    ...children,
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFormField(String label, IconData icon, {bool isTextArea = false}) {
    return Container(
      height: isTextArea ? 120 : 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            ShimmerBox(width: 24, height: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ShimmerLine(width: label.length * 8.0, height: 14),
                  if (isTextArea) ...[
                    const SizedBox(height: 8),
                    const ShimmerLine(height: 14),
                    const SizedBox(height: 4),
                    const ShimmerLine(width: 200, height: 14),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormFieldRow(List<Widget> fields) {
    return Row(
      children: fields
          .expand((field) => [
                Expanded(child: field),
                if (field != fields.last) const SizedBox(width: 16),
              ])
          .toList(),
    );
  }
}

// Modern activity item shimmer with enhanced design
class _ModernActivityItemShimmer extends StatelessWidget {
  final Duration delay;

  const _ModernActivityItemShimmer({required this.delay});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 600 + delay.inMilliseconds),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 10 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isDark
                      ? [
                          const Color(0xFF374151).withValues(alpha: 0.5),
                          const Color(0xFF1F2937).withValues(alpha: 0.3),
                        ]
                      : [
                          const Color(0xFFF9FAFB),
                          const Color(0xFFFFFFFF),
                        ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDark
                      ? const Color(0xFF374151)
                      : const Color(0xFFE5E7EB),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  // Activity icon
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      gradient: RadialGradient(
                        colors: isDark
                            ? [
                                const Color(0xFF4F46E5),
                                const Color(0xFF3B82F6),
                              ]
                            : [
                                const Color(0xFF6366F1),
                                const Color(0xFF8B5CF6),
                              ],
                      ),
                      borderRadius: BorderRadius.circular(18),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Activity content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const ShimmerLine(width: 160, height: 16),
                        const SizedBox(height: 4),
                        const ShimmerLine(width: 120, height: 12),
                      ],
                    ),
                  ),
                  // Time stamp
                  const ShimmerLine(width: 50, height: 12),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
